#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
M3U8批量下载器 - 简化版
用于批量下载洋葱培优课程的加密m3u8视频文件

功能特性：
- 扫描所有m3u8文件
- 自动解密AES-128加密的TS片段
- 多线程下载
- 自动合并为MP4视频
- 保持原有目录结构

作者：AI Assistant
版本：2.0.0 (简化版)
"""

import sys
import re
import base64
import requests
from pathlib import Path
from concurrent.futures import ThreadPoolExecutor, as_completed
import subprocess

try:
    from Crypto.Cipher import AES
    CRYPTO_AVAILABLE = True
except ImportError:
    print("错误：需要安装 pycryptodome")
    print("请运行：pip install pycryptodome")
    sys.exit(1)

try:
    from colorama import init, Fore, Style
    init(autoreset=True)
    COLORAMA_AVAILABLE = True
except ImportError:
    COLORAMA_AVAILABLE = False
    class Fore:
        RED = GREEN = YELLOW = BLUE = MAGENTA = CYAN = WHITE = RESET = ""
    class Style:
        DIM = NORMAL = BRIGHT = RESET_ALL = ""

try:
    from tqdm import tqdm
    TQDM_AVAILABLE = True
except ImportError:
    TQDM_AVAILABLE = False


# 全局配置
CONFIG = {
    "download_dir": "downloads",
    "max_concurrent": 5,
    "max_retries": 3,
    "ffmpeg_path": "./ffmpeg.exe",
    "chunk_size": 1024 * 1024,  # 1MB
    "timeout": 30
}


def parse_m3u8(m3u8_path):
    """解析M3U8文件，提取密钥和TS片段信息"""
    try:
        with open(m3u8_path, 'r', encoding='utf-8') as f:
            content = f.read()

        # 提取AES密钥
        key_match = re.search(r'#EXT-X-KEY:METHOD=AES-128,URI="data:text/plain;base64,([^"]+)"', content)
        if not key_match:
            print(f"{Fore.RED}未找到加密密钥: {m3u8_path}")
            return None, []

        # 解码base64密钥
        key_b64 = key_match.group(1)
        try:
            aes_key = base64.b64decode(key_b64)
        except Exception as e:
            print(f"{Fore.RED}密钥解码失败: {e}")
            return None, []

        # 提取TS片段URL
        ts_urls = []
        lines = content.split('\n')
        for line in lines:
            line = line.strip()
            if line and not line.startswith('#'):
                ts_urls.append(line)

        print(f"{Fore.GREEN}解析成功: {len(ts_urls)} 个片段, 密钥长度: {len(aes_key)} 字节")
        return aes_key, ts_urls

    except Exception as e:
        print(f"{Fore.RED}解析M3U8文件失败: {e}")
        return None, []


def decrypt_ts_data(encrypted_data, aes_key):
    """解密TS片段数据"""
    try:
        # 使用AES-128-CBC，IV为全零
        iv = b'\x00' * 16
        cipher = AES.new(aes_key, AES.MODE_CBC, iv)
        decrypted_data = cipher.decrypt(encrypted_data)

        # 移除PKCS7填充
        padding_length = decrypted_data[-1]
        if padding_length <= 16:
            decrypted_data = decrypted_data[:-padding_length]

        return decrypted_data
    except Exception as e:
        print(f"{Fore.RED}解密失败: {e}")
        return None


def download_ts_segment(url, aes_key, segment_index, temp_dir):
    """下载并解密单个TS片段"""
    try:
        # 下载TS片段
        response = requests.get(url, timeout=CONFIG["timeout"], stream=True)
        response.raise_for_status()

        encrypted_data = response.content

        # 解密数据
        decrypted_data = decrypt_ts_data(encrypted_data, aes_key)
        if decrypted_data is None:
            return False, f"解密失败: segment_{segment_index:04d}"

        # 保存解密后的片段
        segment_file = temp_dir / f"segment_{segment_index:04d}.ts"
        with open(segment_file, 'wb') as f:
            f.write(decrypted_data)

        return True, str(segment_file)

    except Exception as e:
        return False, f"下载失败: {e}"


def download_m3u8_video(m3u8_path, output_path):
    """下载单个M3U8视频"""
    print(f"{Fore.CYAN}开始处理: {m3u8_path}")

    # 解析M3U8文件
    aes_key, ts_urls = parse_m3u8(m3u8_path)
    if not aes_key or not ts_urls:
        print(f"{Fore.RED}解析失败，跳过: {m3u8_path}")
        return False

    # 创建临时目录
    temp_dir = Path(output_path).parent / f"temp_{Path(output_path).stem}"
    temp_dir.mkdir(exist_ok=True)

    try:
        # 多线程下载TS片段
        successful_segments = []
        failed_count = 0

        print(f"{Fore.YELLOW}开始下载 {len(ts_urls)} 个片段...")

        with ThreadPoolExecutor(max_workers=CONFIG["max_concurrent"]) as executor:
            # 提交所有下载任务
            future_to_index = {
                executor.submit(download_ts_segment, url, aes_key, i, temp_dir): i
                for i, url in enumerate(ts_urls)
            }

            # 处理完成的任务
            if TQDM_AVAILABLE:
                progress_bar = tqdm(total=len(ts_urls), desc="下载进度", unit="片段")

            for future in as_completed(future_to_index):
                index = future_to_index[future]
                try:
                    success, result = future.result()
                    if success:
                        successful_segments.append((index, result))
                    else:
                        print(f"{Fore.RED}片段 {index} 失败: {result}")
                        failed_count += 1
                except Exception as e:
                    print(f"{Fore.RED}片段 {index} 异常: {e}")
                    failed_count += 1

                if TQDM_AVAILABLE:
                    progress_bar.update(1)

            if TQDM_AVAILABLE:
                progress_bar.close()

        if failed_count > 0:
            print(f"{Fore.YELLOW}警告: {failed_count} 个片段下载失败")

        if not successful_segments:
            print(f"{Fore.RED}没有成功下载的片段")
            return False

        # 按顺序排序片段
        successful_segments.sort(key=lambda x: x[0])

        # 合并视频片段
        print(f"{Fore.CYAN}开始合并视频...")
        success = merge_ts_segments([seg[1] for seg in successful_segments], output_path)

        return success

    finally:
        # 清理临时文件
        try:
            import shutil
            shutil.rmtree(temp_dir)
        except:
            pass


def merge_ts_segments(segment_files, output_path):
    """使用ffmpeg合并TS片段为MP4视频"""
    try:
        # 检查ffmpeg是否存在
        ffmpeg_path = CONFIG["ffmpeg_path"]
        if not Path(ffmpeg_path).exists():
            print(f"{Fore.RED}FFmpeg未找到: {ffmpeg_path}")
            return False

        # 创建临时的文件列表
        filelist_path = Path(output_path).parent / "filelist.txt"
        with open(filelist_path, 'w', encoding='utf-8') as f:
            for segment_file in segment_files:
                # 使用相对路径，避免路径问题
                f.write(f"file '{Path(segment_file).name}'\n")

        # 构建ffmpeg命令
        cmd = [
            ffmpeg_path,
            "-f", "concat",
            "-safe", "0",
            "-i", str(filelist_path),
            "-c", "copy",
            "-y",  # 覆盖输出文件
            str(output_path)
        ]

        # 执行ffmpeg命令
        result = subprocess.run(
            cmd,
            cwd=str(Path(segment_files[0]).parent),  # 在片段目录中执行
            capture_output=True,
            text=True,
            encoding='utf-8'
        )

        # 清理临时文件列表
        try:
            filelist_path.unlink()
        except:
            pass

        if result.returncode == 0:
            print(f"{Fore.GREEN}视频合并成功: {output_path}")
            return True
        else:
            print(f"{Fore.RED}FFmpeg错误: {result.stderr}")
            return False

    except Exception as e:
        print(f"{Fore.RED}合并失败: {e}")
        return False


def scan_m3u8_files(root_path="."):
    """扫描所有M3U8文件"""
    print(f"{Fore.CYAN}正在扫描M3U8文件...")

    m3u8_files = []
    root = Path(root_path)

    for m3u8_path in root.rglob("*.m3u8"):
        if m3u8_path.is_file():
            relative_path = m3u8_path.relative_to(root)
            m3u8_files.append({
                'path': str(m3u8_path),
                'relative_path': str(relative_path),
                'name': m3u8_path.stem
            })

    print(f"{Fore.GREEN}找到 {len(m3u8_files)} 个M3U8文件")
    return m3u8_files


def main():
    """主函数"""
    print(f"{Fore.GREEN}M3U8批量下载器 v2.0.0 (简化版)")
    print(f"{Fore.CYAN}支持AES-128加密视频下载")
    print("=" * 50)

    # 扫描M3U8文件
    m3u8_files = scan_m3u8_files()
    if not m3u8_files:
        print(f"{Fore.RED}未找到任何M3U8文件")
        return

    # 显示菜单
    while True:
        print(f"\n{Fore.YELLOW}=== 主菜单 ===")
        print(f"{Fore.WHITE}1. 下载所有视频")
        print(f"{Fore.WHITE}2. 选择特定目录下载")
        print(f"{Fore.WHITE}3. 下载单个视频")
        print(f"{Fore.WHITE}4. 查看文件列表")
        print(f"{Fore.WHITE}5. 退出程序")

        try:
            choice = input(f"\n{Fore.GREEN}请选择操作 (1-5): ").strip()

            if choice == '1':
                download_all_videos(m3u8_files)
            elif choice == '2':
                download_by_directory(m3u8_files)
            elif choice == '3':
                download_single_video(m3u8_files)
            elif choice == '4':
                show_file_list(m3u8_files)
            elif choice == '5':
                print(f"{Fore.GREEN}感谢使用！")
                break
            else:
                print(f"{Fore.RED}无效选择，请输入1-5之间的数字")

        except KeyboardInterrupt:
            print(f"\n{Fore.YELLOW}程序已取消")
            break
        except Exception as e:
            print(f"{Fore.RED}程序异常: {e}")


def download_all_videos(m3u8_files):
    """下载所有视频"""
    print(f"\n{Fore.CYAN}准备下载 {len(m3u8_files)} 个视频...")

    confirm = input(f"{Fore.GREEN}确认开始下载所有视频? (y/n): ").strip().lower()
    if confirm not in ['y', 'yes', '是']:
        return

    download_dir = Path(CONFIG["download_dir"])
    download_dir.mkdir(exist_ok=True)

    success_count = 0
    failed_count = 0

    for i, m3u8_file in enumerate(m3u8_files, 1):
        print(f"\n{Fore.CYAN}[{i}/{len(m3u8_files)}] 处理: {m3u8_file['name']}")

        # 构建输出路径
        relative_path = Path(m3u8_file['relative_path'])
        output_path = download_dir / relative_path.parent / f"{relative_path.stem}.mp4"
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 检查是否已存在
        if output_path.exists():
            print(f"{Fore.YELLOW}文件已存在，跳过: {output_path}")
            continue

        # 下载视频
        if download_m3u8_video(m3u8_file['path'], output_path):
            success_count += 1
        else:
            failed_count += 1

    print(f"\n{Fore.GREEN}下载完成!")
    print(f"{Fore.WHITE}成功: {success_count} 个")
    print(f"{Fore.RED}失败: {failed_count} 个")


def download_by_directory(m3u8_files):
    """按目录下载"""
    # 获取所有目录
    directories = set()
    for m3u8_file in m3u8_files:
        dir_path = str(Path(m3u8_file['relative_path']).parent)
        if dir_path != '.':
            directories.add(dir_path)

    directories = sorted(list(directories))

    if not directories:
        print(f"{Fore.RED}未找到任何目录")
        return

    print(f"\n{Fore.YELLOW}=== 选择目录 ===")
    for i, directory in enumerate(directories, 1):
        count = len([f for f in m3u8_files if str(Path(f['relative_path']).parent) == directory])
        print(f"{Fore.WHITE}{i}. {directory} ({count} 个文件)")

    try:
        choice = input(f"\n{Fore.GREEN}请选择目录编号: ").strip()
        if not choice.isdigit():
            print(f"{Fore.RED}无效输入")
            return

        index = int(choice) - 1
        if index < 0 or index >= len(directories):
            print(f"{Fore.RED}无效选择")
            return

        selected_dir = directories[index]
        selected_files = [f for f in m3u8_files if str(Path(f['relative_path']).parent) == selected_dir]

        print(f"\n{Fore.CYAN}选择的目录: {selected_dir}")
        print(f"{Fore.WHITE}包含 {len(selected_files)} 个文件")

        confirm = input(f"{Fore.GREEN}确认下载? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            return

        download_selected_files(selected_files)

    except ValueError:
        print(f"{Fore.RED}无效输入")
    except KeyboardInterrupt:
        return


def download_single_video(m3u8_files):
    """下载单个视频"""
    print(f"\n{Fore.YELLOW}=== 选择视频 ===")

    # 显示前20个文件
    display_count = min(20, len(m3u8_files))
    for i in range(display_count):
        m3u8_file = m3u8_files[i]
        print(f"{Fore.WHITE}{i+1}. {m3u8_file['name']}")

    if len(m3u8_files) > display_count:
        print(f"{Fore.YELLOW}... 还有 {len(m3u8_files) - display_count} 个文件")
        print(f"{Fore.WHITE}输入文件名关键词进行搜索")

    try:
        choice = input(f"\n{Fore.GREEN}请选择 (编号/关键词): ").strip()

        if choice.isdigit():
            # 按编号选择
            index = int(choice) - 1
            if index < 0 or index >= len(m3u8_files):
                print(f"{Fore.RED}无效选择")
                return
            selected_file = m3u8_files[index]
        else:
            # 按关键词搜索
            matches = [f for f in m3u8_files if choice.lower() in f['name'].lower()]
            if not matches:
                print(f"{Fore.RED}未找到匹配的文件")
                return
            elif len(matches) == 1:
                selected_file = matches[0]
            else:
                print(f"\n{Fore.YELLOW}找到 {len(matches)} 个匹配文件:")
                for i, match in enumerate(matches[:10], 1):
                    print(f"{Fore.WHITE}{i}. {match['name']}")

                sub_choice = input(f"{Fore.GREEN}请选择编号: ").strip()
                if not sub_choice.isdigit():
                    print(f"{Fore.RED}无效输入")
                    return

                sub_index = int(sub_choice) - 1
                if sub_index < 0 or sub_index >= len(matches):
                    print(f"{Fore.RED}无效选择")
                    return

                selected_file = matches[sub_index]

        print(f"\n{Fore.CYAN}选择的文件: {selected_file['name']}")
        confirm = input(f"{Fore.GREEN}确认下载? (y/n): ").strip().lower()
        if confirm not in ['y', 'yes', '是']:
            return

        download_selected_files([selected_file])

    except ValueError:
        print(f"{Fore.RED}无效输入")
    except KeyboardInterrupt:
        return


def show_file_list(m3u8_files):
    """显示文件列表"""
    print(f"\n{Fore.YELLOW}=== 文件列表 ===")
    print(f"{Fore.WHITE}总计: {len(m3u8_files)} 个M3U8文件")

    # 按目录分组显示
    directories = {}
    for m3u8_file in m3u8_files:
        dir_path = str(Path(m3u8_file['relative_path']).parent)
        if dir_path not in directories:
            directories[dir_path] = []
        directories[dir_path].append(m3u8_file)

    for directory, files in sorted(directories.items()):
        print(f"\n{Fore.CYAN}{directory}/ ({len(files)} 个文件)")
        for i, file in enumerate(files[:5], 1):
            print(f"  {i}. {file['name']}")
        if len(files) > 5:
            print(f"  ... 还有 {len(files) - 5} 个文件")


def download_selected_files(selected_files):
    """下载选中的文件"""
    download_dir = Path(CONFIG["download_dir"])
    download_dir.mkdir(exist_ok=True)

    success_count = 0
    failed_count = 0

    for i, m3u8_file in enumerate(selected_files, 1):
        print(f"\n{Fore.CYAN}[{i}/{len(selected_files)}] 处理: {m3u8_file['name']}")

        # 构建输出路径
        relative_path = Path(m3u8_file['relative_path'])
        output_path = download_dir / relative_path.parent / f"{relative_path.stem}.mp4"
        output_path.parent.mkdir(parents=True, exist_ok=True)

        # 检查是否已存在
        if output_path.exists():
            print(f"{Fore.YELLOW}文件已存在，跳过: {output_path}")
            continue

        # 下载视频
        if download_m3u8_video(m3u8_file['path'], output_path):
            success_count += 1
        else:
            failed_count += 1

    print(f"\n{Fore.GREEN}下载完成!")
    print(f"{Fore.WHITE}成功: {success_count} 个")
    print(f"{Fore.RED}失败: {failed_count} 个")


if __name__ == "__main__":
    main()
